import express from "express";
import { models } from "@/schemas";
import { log } from "@/services/logger";
import { IntentionalError } from "@/utils/errors";

const updateNoteHandler = async (
  req: express.Request,
  res: express.Response
) => {
  try {
    const { id } = req.params;
    const { content, title } = req.body;
    const currentUser = res.locals.user;

    if (!currentUser) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    const note = await models.Note.update(
      {
        content,
        title,
        updatedById: currentUser.id,
      },
      {
        where: { id },
      }
    );

    if (!note) {
      res.status(404).json({ error: "Note not found" });
      return;
    }

    res.status(200).json(note);
  } catch (error) {
    log.error("[updateNoteHandler] Failed to update note", error);
    throw new IntentionalError("Failed to update note");
  }
};

export default updateNoteHandler;
